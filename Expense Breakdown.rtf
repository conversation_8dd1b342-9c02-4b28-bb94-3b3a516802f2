{\rtf1\ansi\ansicpg1252\cocoartf2761
\cocoatextscaling0\cocoaplatform0{\fonttbl\f0\fswiss\fcharset0 Helvetica;\f1\fnil\fcharset0 AppleColorEmoji;}
{\colortbl;\red255\green255\blue255;}
{\*\expandedcolortbl;;}
\paperw11900\paperh16840\margl1440\margr1440\vieww11520\viewh8400\viewkind0
\pard\tx720\tx1440\tx2160\tx2880\tx3600\tx4320\tx5040\tx5760\tx6480\tx7200\tx7920\tx8640\pardirnatural\partightenfactor0

\f0\fs24 \cf0 import requests\
import pandas as pd\
from datetime import datetime\
\
# -----------------------\
# CONFIGURATION\
# -----------------------\
COMPANY_ID = "<your_company_id>"\
ACCESS_TOKEN = "<your_access_token>"   # OAuth2 token\
API_BASE = f"https://quickbooks.api.intuit.com/v3/company/\{COMPANY_ID\}"\
\
HEADERS = \{\
    "Authorization": f"Bearer \{ACCESS_TOKEN\}",\
    "Accept": "application/json",\
    "Content-Type": "application/text"\
\}\
\
# -----------------------\
# HELPER: RUN QUERY\
# -----------------------\
def run_query(query):\
    url = f"\{API_BASE\}/query"\
    resp = requests.post(url, headers=HEADERS, data=query)\
    resp.raise_for_status()\
    return resp.json()\
\
# -----------------------\
# FETCH EXPENSE TRANSACTIONS\
# -----------------------\
def get_expenses(start_date):\
    q = f"select Id, TxnDate, TotalAmt, Line from Expense where TxnDate >= '\{start_date\}'"\
    return run_query(q).get("QueryResponse", \{\}).get("Expense", [])\
\
def get_bills(start_date):\
    q = f"select Id, TxnDate, TotalAmt, Line from Bill where TxnDate >= '\{start_date\}'"\
    return run_query(q).get("QueryResponse", \{\}).get("Bill", [])\
\
def get_purchases(start_date):\
    q = f"select Id, TxnDate, TotalAmt, Line from Purchase where TxnDate >= '\{start_date\}'"\
    return run_query(q).get("QueryResponse", \{\}).get("Purchase", [])\
\
def get_checks(start_date):\
    q = f"select Id, TxnDate, TotalAmt, Line from Check where TxnDate >= '\{start_date\}'"\
    return run_query(q).get("QueryResponse", \{\}).get("Check", [])\
\
def get_journals(start_date):\
    q = f"select Id, TxnDate, TotalAmt, Line from JournalEntry where TxnDate >= '\{start_date\}'"\
    return run_query(q).get("QueryResponse", \{\}).get("JournalEntry", [])\
\
def get_accounts():\
    q = "select Id, Name, AccountType, AccountSubType from Account"\
    return run_query(q).get("QueryResponse", \{\}).get("Account", [])\
\
# -----------------------\
# CLASSIFY EXPENSES\
# -----------------------\
def classify_expense(account_type, account_subtype):\
    if account_subtype in ["PayrollExpenses", "Wages", "Salaries", "PayrollTaxExpense"]:\
        return "Payroll"\
    elif account_subtype in ["HealthInsurance", "EmployeeBenefits", "MedicalExpense"]:\
        return "Medical"\
    elif account_type in ["Expense", "OtherExpense"]:\
        return "Operating"\
    else:\
        return "Other"\
\
# -----------------------\
# BUILD EXPENSE DATASET\
# -----------------------\
def build_expense_breakdown(start_date="2025-01-01"):\
    # Fetch data\
    expenses = get_expenses(start_date)\
    bills = get_bills(start_date)\
    purchases = get_purchases(start_date)\
    checks = get_checks(start_date)\
    journals = get_journals(start_date)\
    accounts = get_accounts()\
\
    # Account mapping\
    account_map = \{a["Id"]: (a["AccountType"], a.get("AccountSubType")) for a in accounts\}\
\
    data = []\
\
    def extract_lines(records, source):\
        for rec in records:\
            txn_date = rec["TxnDate"]\
            for line in rec.get("Line", []):\
                acct_id = line.get("AccountRef", \{\}).get("value")\
                if acct_id and acct_id in account_map:\
                    acct_type, acct_subtype = account_map[acct_id]\
                    category = classify_expense(acct_type, acct_subtype)\
                    amount = float(line.get("Amount", 0))\
                    data.append([txn_date, category, amount, source])\
\
    # Collect from all sources\
    extract_lines(expenses, "Expense")\
    extract_lines(bills, "Bill")\
    extract_lines(purchases, "Purchase")\
    extract_lines(checks, "Check")\
    extract_lines(journals, "JournalEntry")\
\
    # Build DataFrame\
    df = pd.DataFrame(data, columns=["Date", "Category", "Amount", "Source"])\
    df["Date"] = pd.to_datetime(df["Date"])\
    return df\
\
# -----------------------\
# RUN + EXPORT\
# -----------------------\
if __name__ == "__main__":\
    start_date = "2024-01-01"  # 
\f1 \uc0\u55357 \u56392 
\f0  change this as needed\
    df = build_expense_breakdown(start_date)\
\
    # Export daily-level expenses\
    df.to_csv("expenses_breakdown_daily.csv", index=False)\
\
    print("
\f1 \uc0\u9989 
\f0  Exported: expenses_breakdown_daily.csv")\
    print(df.head())\
}