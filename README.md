# QuickBooks Online Data Analysis Scripts

This project contains two Python scripts for analyzing QuickBooks Online (QBO) financial data and storing results in a PostgreSQL database:

1. **Revenue_Breakdown.py** - Analyzes revenue from invoices and sales receipts
2. **Expense_Breakdown.py** - Analyzes expenses from various transaction types

## Prerequisites

### 1. Python Environment

- Python 3.7 or higher
- Required packages: `requests`, `pandas`, `psycopg2-binary`

### 2. PostgreSQL Database

- PostgreSQL server running on localhost:5433
- Database: `postgres`
- User: `nehalkapadia` with password: `Nehal2567`

### 3. QuickBooks Online API Access

You need:

- **Company ID (realmId)** - Your QuickBooks company identifier
- **OAuth2 Access Token** - Valid access token for API authentication

## Setup Instructions

### 1. Install Dependencies

```bash
pip install -r requirements.txt
```

### 2. Set Up Database

Run the database migrations to create the required tables:

```bash
python run_migrations.py
```

This will create the following tables:

- `revenue_transactions` - Raw revenue transaction details
- `total_revenue_daily` - Daily total revenue summary
- `revenue_breakdown_daily` - Daily revenue by account
- `expense_transactions` - Daily expense data with categories

### 3. Configure API Credentials

Edit both Python files and replace the placeholder values:

```python
COMPANY_ID = "<your_company_id>"      # Replace with your actual Company ID
ACCESS_TOKEN = "<your_access_token>"  # Replace with your OAuth2 token
```

### 4. Adjust Date Range (Optional)

Modify the `start_date` variable in the main execution section:

```python
start_date = "2024-01-01"  # Change to your desired start date
```

## Execution

### Run Revenue Analysis

```bash
python Revenue_Breakdown.py
```

**Database tables populated:**

- `revenue_transactions` - Raw revenue transaction details
- `total_revenue_daily` - Daily total revenue summary
- `revenue_breakdown_daily` - Daily revenue by account

### Run Expense Analysis

```bash
python Expense_Breakdown.py
```

**Database tables populated:**

- `expense_transactions` - Daily expense data with categories

**Note:** CSV export is commented out but can be enabled by uncommenting the relevant lines in the scripts.

## Script Details

### Revenue_Breakdown.py

- Fetches invoices and sales receipts from QBO
- Filters for income accounts only
- Groups revenue by date, customer, and revenue account
- Creates three levels of analysis (raw, daily totals, daily breakdown)

### Expense_Breakdown.py

- Fetches expenses, bills, purchases, checks, and journal entries
- Classifies expenses into categories:
  - **Payroll**: Wages, salaries, payroll taxes
  - **Medical**: Health insurance, employee benefits
  - **Operating**: General business expenses
  - **Other**: Miscellaneous expenses
- Exports daily expense breakdown with categories

## Getting QuickBooks API Credentials

1. **Company ID**: Found in your QuickBooks Online URL or through the API
2. **Access Token**: Requires OAuth2 flow setup through Intuit Developer account
   - Visit: https://developer.intuit.com/
   - Create an app and follow OAuth2 documentation

## Troubleshooting

- **Authentication errors**: Verify your Company ID and Access Token
- **Date format issues**: Ensure dates are in YYYY-MM-DD format
- **Missing data**: Check if your QBO account has the expected transaction types
- **API rate limits**: QBO has rate limits; add delays if needed for large datasets

## Data Output Format

All CSV files include:

- Date columns in YYYY-MM-DD format
- Amount columns as decimal numbers
- Category/classification columns for analysis
- Source columns indicating transaction type
