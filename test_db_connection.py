#!/usr/bin/env python3
"""
Database Connection Test
Tests the PostgreSQL database connection and shows table information
"""

import psycopg2
from psycopg2.extras import RealDictCursor

# Database configuration
DB_CONFIG = {
    "user": "nehalkapadia",
    "host": "localhost",
    "database": "postgres",
    "password": "Nehal2567",
    "port": 5433
}

def test_connection():
    """Test database connection and show table info"""
    try:
        # Connect to database
        conn = psycopg2.connect(**DB_CONFIG)
        cursor = conn.cursor(cursor_factory=RealDictCursor)
        
        print("🔗 Successfully connected to PostgreSQL database!")
        
        # Check if tables exist
        tables_to_check = [
            'revenue_transactions',
            'total_revenue_daily', 
            'revenue_breakdown_daily',
            'expense_transactions'
        ]
        
        print("\n📊 Checking tables:")
        for table in tables_to_check:
            cursor.execute(f"""
                SELECT COUNT(*) as count 
                FROM information_schema.tables 
                WHERE table_name = '{table}'
            """)
            result = cursor.fetchone()
            
            if result['count'] > 0:
                # Get row count
                cursor.execute(f"SELECT COUNT(*) as row_count FROM {table}")
                row_count = cursor.fetchone()['row_count']
                print(f"  ✅ {table} - exists ({row_count} rows)")
            else:
                print(f"  ❌ {table} - does not exist")
        
        print("\n🎉 Database connection test completed!")
        
    except psycopg2.Error as e:
        print(f"❌ Database error: {str(e)}")
        print("💡 Make sure PostgreSQL is running and credentials are correct")
    except Exception as e:
        print(f"❌ Unexpected error: {str(e)}")
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()

if __name__ == "__main__":
    test_connection()
