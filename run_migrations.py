#!/usr/bin/env python3
"""
Database Migration Runner
Runs all SQL migration files in the migrations directory
"""

import os
import psycopg2
from psycopg2.extensions import ISOLATION_LEVEL_AUTOCOMMIT
import glob

# Database configuration
DB_CONFIG = {
    "user": "nehal<PERSON>padia",
    "host": "localhost", 
    "database": "postgres",
    "password": "Nehal2567",
    "port": 5433
}

def run_migrations():
    """Run all migration files in order"""
    try:
        # Connect to database
        conn = psycopg2.connect(**DB_CONFIG)
        conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
        cursor = conn.cursor()
        
        print("🔗 Connected to PostgreSQL database")
        
        # Get all migration files and sort them
        migration_files = sorted(glob.glob("migrations/*.sql"))
        
        if not migration_files:
            print("❌ No migration files found in migrations/ directory")
            return
        
        print(f"📁 Found {len(migration_files)} migration files")
        
        # Run each migration
        for migration_file in migration_files:
            print(f"🔄 Running migration: {migration_file}")
            
            with open(migration_file, 'r') as f:
                sql_content = f.read()
            
            try:
                cursor.execute(sql_content)
                print(f"✅ Successfully executed: {migration_file}")
            except Exception as e:
                print(f"❌ Error executing {migration_file}: {str(e)}")
                raise
        
        print("🎉 All migrations completed successfully!")
        
    except psycopg2.Error as e:
        print(f"❌ Database error: {str(e)}")
        raise
    except Exception as e:
        print(f"❌ Unexpected error: {str(e)}")
        raise
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()
        print("🔌 Database connection closed")

if __name__ == "__main__":
    run_migrations()
