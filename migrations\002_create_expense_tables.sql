-- Migration: Create Expense Tables
-- Description: Creates tables for storing QuickBooks expense data

-- Table for expense transactions
CREATE TABLE IF NOT EXISTS expense_transactions (
    id SERIAL PRIMARY KEY,
    transaction_date DATE NOT NULL,
    category VARCHAR(100) NOT NULL, -- 'Payroll', 'Medical', 'Operating', 'Other'
    amount DECIMAL(15, 2) NOT NULL,
    source VARCHAR(50) NOT NULL, -- 'Expense', 'Bill', 'Purchase', 'Check', 'JournalEntry'
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_expense_transactions_date ON expense_transactions(transaction_date);
CREATE INDEX IF NOT EXISTS idx_expense_transactions_category ON expense_transactions(category);
CREATE INDEX IF NOT EXISTS idx_expense_transactions_source ON expense_transactions(source);

-- Create trigger to update updated_at timestamp
CREATE TRIGGER update_expense_transactions_updated_at BEFORE UPDATE ON expense_transactions FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
