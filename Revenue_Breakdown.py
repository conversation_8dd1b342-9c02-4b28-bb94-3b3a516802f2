import requests
import pandas as pd
from datetime import datetime
import psycopg2
from psycopg2.extras import execute_values

# -----------------------
# CONFIGURATION
# -----------------------
COMPANY_ID = "9341455145768938"          # Replace with your QuickBooks Company ID (realmId)
ACCESS_TOKEN = "eyJhbGciOiJkaXIiLCJlbmMiOiJBMTI4Q0JDLUhTMjU2IiwieC5vcmciOiJIMCJ9..WSujmhzrjQ6Kaf9P5_iiyQ.ho3gIQtGnP_TIvbP-_fMT7Gxh4QOhtszR41dAIAQpo2gn9gqBzsiXT-G1QoYVQdxZrfB532QwthkKzmBxYPeqIPKPIGtS9T_YKFduCNgsaWmeagxdJjTgpIVJCSRhc76WRnSTzPg2BX28wbyYSWBtuMjnyEqHeVE0HLNOQhtk_gbcF5bNR5YatquBKEEFtYsyVpPbj6bBM7g66Dv-teGHi0FYlzqt2YbX4sauXFYB8GsY2wnaqNowuAoJ0WRChlKqv7Y5ekqpS_MUth2f46VMT3yiVQ3xeDynbmblfqqwVWEInDgaBvjiwESbBh5sqbmkIDa9IoxIZXHIvaL6Or_Q3vrX_WTxk5BJwnhTfYYJ_uE9kpBgip4q3_umT1tUuXr6Axv2Ftu5rPiLt455wCncLhMXzqe85H0pCLwEklX6w8-IHb8ZI0GMN3SzpP9lzLIWx0_Spx8yAXnU8QgP7Gdu08uZfScpJv5tI6qmbOJfuY.sLk4FrIytGwukQsYLAosNA"      # Replace with your OAuth2 access token
# API_BASE = f"https://quickbooks.api.intuit.com/v3/company/{COMPANY_ID}"
API_BASE = f"https://sandbox-quickbooks.api.intuit.com/v3/company/{COMPANY_ID}"

HEADERS = {
    "Authorization": f"Bearer {ACCESS_TOKEN}",
    "Accept": "application/json",
    "Content-Type": "application/text"
}

# Database configuration
DB_CONFIG = {
    "user": "nehalkapadia",
    "host": "localhost",
    "database": "postgres",
    "password": "Nehal2567",
    "port": 5433
}

# -----------------------
# HELPER: RUN QUERY
# -----------------------
def run_query(query):
    url = f"{API_BASE}/query"
    resp = requests.post(url, headers=HEADERS, data=query)
    resp.raise_for_status()
    return resp.json()

# -----------------------
# FETCH REVENUE TRANSACTIONS
# -----------------------
def get_invoices(start_date):
    q = f"select Id, TxnDate, CustomerRef, TotalAmt, Line from Invoice where TxnDate >= '{start_date}'"
    return run_query(q).get("QueryResponse", {}).get("Invoice", [])

def get_salesreceipts(start_date):
    q = f"select Id, TxnDate, CustomerRef, TotalAmt, Line from SalesReceipt where TxnDate >= '{start_date}'"
    return run_query(q).get("QueryResponse", {}).get("SalesReceipt", [])

def get_accounts():
    q = "select Id, Name, AccountType, AccountSubType from Account"
    return run_query(q).get("QueryResponse", {}).get("Account", [])

# -----------------------
# DATABASE HELPER FUNCTIONS
# -----------------------
def get_db_connection():
    """Get database connection"""
    return psycopg2.connect(**DB_CONFIG)

def save_to_database(raw_df, total_df, breakdown_df):
    """Save DataFrames to database tables"""
    conn = None
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # Clear existing data (optional - remove if you want to append)
        cursor.execute("DELETE FROM revenue_transactions")
        cursor.execute("DELETE FROM total_revenue_daily")
        cursor.execute("DELETE FROM revenue_breakdown_daily")

        # Insert raw revenue transactions
        if not raw_df.empty:
            raw_data = [
                (row['Date'].date(), row['Customer'], row['RevenueAccount'],
                 float(row['Amount']), row['Source'])
                for _, row in raw_df.iterrows()
            ]
            execute_values(
                cursor,
                "INSERT INTO revenue_transactions (transaction_date, customer, revenue_account, amount, source) VALUES %s",
                raw_data
            )
            print(f"✅ Inserted {len(raw_data)} revenue transactions")

        # Insert daily totals
        if not total_df.empty:
            total_data = [
                (row['Date'].date(), float(row['TotalRevenue']))
                for _, row in total_df.iterrows()
            ]
            execute_values(
                cursor,
                "INSERT INTO total_revenue_daily (transaction_date, total_revenue) VALUES %s ON CONFLICT (transaction_date) DO UPDATE SET total_revenue = EXCLUDED.total_revenue",
                total_data
            )
            print(f"✅ Inserted {len(total_data)} daily revenue totals")

        # Insert daily breakdown
        if not breakdown_df.empty:
            breakdown_data = [
                (row['Date'].date(), row['RevenueAccount'], float(row['Amount']))
                for _, row in breakdown_df.iterrows()
            ]
            execute_values(
                cursor,
                "INSERT INTO revenue_breakdown_daily (transaction_date, revenue_account, amount) VALUES %s ON CONFLICT (transaction_date, revenue_account) DO UPDATE SET amount = EXCLUDED.amount",
                breakdown_data
            )
            print(f"✅ Inserted {len(breakdown_data)} daily revenue breakdown records")

        conn.commit()
        print("🎉 All revenue data saved to database successfully!")

    except Exception as e:
        if conn:
            conn.rollback()
        print(f"❌ Error saving to database: {str(e)}")
        raise
    finally:
        if conn:
            conn.close()

# -----------------------
# BUILD REVENUE DATASET
# -----------------------
def build_revenue_breakdown(start_date="2025-01-01"):
    invoices = get_invoices(start_date)
    receipts = get_salesreceipts(start_date)
    accounts = get_accounts()

    # Filter only Income Accounts
    income_accounts = {a["Id"]: a for a in accounts if a["AccountType"] in ["Income", "OtherIncome"]}

    data = []

    # Invoices
    for inv in invoices:
        txn_date = inv["TxnDate"]
        cust = inv.get("CustomerRef", {}).get("name", "Unknown")
        for line in inv.get("Line", []):
            acct_id = line.get("SalesItemLineDetail", {}).get("AccountRef", {}).get("value")
            if acct_id in income_accounts:
                acct_name = income_accounts[acct_id]["Name"]
                amount = float(line.get("Amount", 0))
                data.append([txn_date, cust, acct_name, amount, "Invoice"])

    # SalesReceipts
    for rec in receipts:
        txn_date = rec["TxnDate"]
        cust = rec.get("CustomerRef", {}).get("name", "Unknown")
        for line in rec.get("Line", []):
            acct_id = line.get("SalesItemLineDetail", {}).get("AccountRef", {}).get("value")
            if acct_id in income_accounts:
                acct_name = income_accounts[acct_id]["Name"]
                amount = float(line.get("Amount", 0))
                data.append([txn_date, cust, acct_name, amount, "SalesReceipt"])

    df = pd.DataFrame(data, columns=["Date", "Customer", "RevenueAccount", "Amount", "Source"])
    df["Date"] = pd.to_datetime(df["Date"])

    # Daily totals
    total_revenue = df.groupby("Date")["Amount"].sum().reset_index(name="TotalRevenue")

    # Daily breakdown by account
    breakdown = df.groupby(["Date", "RevenueAccount"])["Amount"].sum().reset_index()

    return df, total_revenue, breakdown

# -----------------------
# RUN + EXPORT
# -----------------------
if __name__ == "__main__":
    start_date = "2024-01-01"   # 📅 adjust as needed
    raw_df, total_df, breakdown_df = build_revenue_breakdown(start_date)

    # Save to database
    save_to_database(raw_df, total_df, breakdown_df)

    # CSV export (commented out - uncomment if you also want CSV files)
    # raw_df.to_csv("revenue_transactions.csv", index=False)
    # total_df.to_csv("total_revenue_daily.csv", index=False)
    # breakdown_df.to_csv("revenue_breakdown_daily.csv", index=False)
    # print("✅ CSV files exported:")
    # print("   - revenue_transactions.csv (raw revenue lines)")
    # print("   - total_revenue_daily.csv (daily total revenue)")
    # print("   - revenue_breakdown_daily.csv (daily revenue by account)")

    print("\nSample Total Revenue:\n", total_df.head())