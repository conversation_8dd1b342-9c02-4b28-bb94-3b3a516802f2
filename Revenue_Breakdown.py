import requests
import pandas as pd
from datetime import datetime

# -----------------------
# CONFIGURATION
# -----------------------
COMPANY_ID = "<your_company_id>"          # Replace with your QuickBooks Company ID (realmId)
ACCESS_TOKEN = "<your_access_token>"      # Replace with your OAuth2 access token
API_BASE = f"https://quickbooks.api.intuit.com/v3/company/{COMPANY_ID}"

HEADERS = {
    "Authorization": f"Bearer {ACCESS_TOKEN}",
    "Accept": "application/json",
    "Content-Type": "application/text"
}

# -----------------------
# HELPER: RUN QUERY
# -----------------------
def run_query(query):
    url = f"{API_BASE}/query"
    resp = requests.post(url, headers=HEADERS, data=query)
    resp.raise_for_status()
    return resp.json()

# -----------------------
# FETCH REVENUE TRANSACTIONS
# -----------------------
def get_invoices(start_date):
    q = f"select Id, TxnDate, CustomerRef, TotalAmt, Line from Invoice where TxnDate >= '{start_date}'"
    return run_query(q).get("QueryResponse", {}).get("Invoice", [])

def get_salesreceipts(start_date):
    q = f"select Id, TxnDate, CustomerRef, TotalAmt, Line from SalesReceipt where TxnDate >= '{start_date}'"
    return run_query(q).get("QueryResponse", {}).get("SalesReceipt", [])

def get_accounts():
    q = "select Id, Name, AccountType, AccountSubType from Account"
    return run_query(q).get("QueryResponse", {}).get("Account", [])

# -----------------------
# BUILD REVENUE DATASET
# -----------------------
def build_revenue_breakdown(start_date="2025-01-01"):
    invoices = get_invoices(start_date)
    receipts = get_salesreceipts(start_date)
    accounts = get_accounts()

    # Filter only Income Accounts
    income_accounts = {a["Id"]: a for a in accounts if a["AccountType"] in ["Income", "OtherIncome"]}

    data = []

    # Invoices
    for inv in invoices:
        txn_date = inv["TxnDate"]
        cust = inv.get("CustomerRef", {}).get("name", "Unknown")
        for line in inv.get("Line", []):
            acct_id = line.get("SalesItemLineDetail", {}).get("AccountRef", {}).get("value")
            if acct_id in income_accounts:
                acct_name = income_accounts[acct_id]["Name"]
                amount = float(line.get("Amount", 0))
                data.append([txn_date, cust, acct_name, amount, "Invoice"])

    # SalesReceipts
    for rec in receipts:
        txn_date = rec["TxnDate"]
        cust = rec.get("CustomerRef", {}).get("name", "Unknown")
        for line in rec.get("Line", []):
            acct_id = line.get("SalesItemLineDetail", {}).get("AccountRef", {}).get("value")
            if acct_id in income_accounts:
                acct_name = income_accounts[acct_id]["Name"]
                amount = float(line.get("Amount", 0))
                data.append([txn_date, cust, acct_name, amount, "SalesReceipt"])

    df = pd.DataFrame(data, columns=["Date", "Customer", "RevenueAccount", "Amount", "Source"])
    df["Date"] = pd.to_datetime(df["Date"])

    # Daily totals
    total_revenue = df.groupby("Date")["Amount"].sum().reset_index(name="TotalRevenue")

    # Daily breakdown by account
    breakdown = df.groupby(["Date", "RevenueAccount"])["Amount"].sum().reset_index()

    return df, total_revenue, breakdown

# -----------------------
# RUN + EXPORT
# -----------------------
if __name__ == "__main__":
    start_date = "2024-01-01"   # 📅 adjust as needed
    raw_df, total_df, breakdown_df = build_revenue_breakdown(start_date)

    raw_df.to_csv("revenue_transactions.csv", index=False)
    total_df.to_csv("total_revenue_daily.csv", index=False)
    breakdown_df.to_csv("revenue_breakdown_daily.csv", index=False)

    print("✅ Exported:")
    print("   - revenue_transactions.csv (raw revenue lines)")
    print("   - total_revenue_daily.csv (daily total revenue)")
    print("   - revenue_breakdown_daily.csv (daily revenue by account)")
    print("\nSample Total Revenue:\n", total_df.head())