-- Migration: Create Revenue Tables
-- Description: Creates tables for storing QuickBooks revenue data

-- Table for raw revenue transactions
CREATE TABLE IF NOT EXISTS revenue_transactions (
    id SERIAL PRIMARY KEY,
    transaction_date DATE NOT NULL,
    customer VARCHAR(255),
    revenue_account VARCHAR(255),
    amount DECIMAL(15, 2) NOT NULL,
    source VARCHAR(50) NOT NULL, -- 'Invoice' or 'SalesReceipt'
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Table for daily total revenue
CREATE TABLE IF NOT EXISTS total_revenue_daily (
    id SERIAL PRIMARY KEY,
    transaction_date DATE NOT NULL UNIQUE,
    total_revenue DECIMAL(15, 2) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Table for daily revenue breakdown by account
CREATE TABLE IF NOT EXISTS revenue_breakdown_daily (
    id SERIAL PRIMARY KEY,
    transaction_date DATE NOT NULL,
    revenue_account VARCHAR(255) NOT NULL,
    amount DECIMAL(15, 2) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(transaction_date, revenue_account)
);

-- Create indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_revenue_transactions_date ON revenue_transactions(transaction_date);
CREATE INDEX IF NOT EXISTS idx_revenue_transactions_customer ON revenue_transactions(customer);
CREATE INDEX IF NOT EXISTS idx_revenue_transactions_source ON revenue_transactions(source);
CREATE INDEX IF NOT EXISTS idx_total_revenue_daily_date ON total_revenue_daily(transaction_date);
CREATE INDEX IF NOT EXISTS idx_revenue_breakdown_daily_date ON revenue_breakdown_daily(transaction_date);
CREATE INDEX IF NOT EXISTS idx_revenue_breakdown_daily_account ON revenue_breakdown_daily(revenue_account);

-- Create trigger to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_revenue_transactions_updated_at BEFORE UPDATE ON revenue_transactions FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_total_revenue_daily_updated_at BEFORE UPDATE ON total_revenue_daily FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_revenue_breakdown_daily_updated_at BEFORE UPDATE ON revenue_breakdown_daily FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
